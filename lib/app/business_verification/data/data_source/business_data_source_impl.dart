import 'package:td_commons_flutter/models/index.dart';
import 'package:td_flutter_core/td_flutter_core.dart';
import 'package:td_procurement/app/auth/domain/params/initiate_stripe_response.dart';
import 'package:td_procurement/app/business_verification/data/data_source/business_data_source.dart';
import 'package:td_procurement/app/business_verification/domain/params/bank_data.dart';
import 'package:td_procurement/app/business_verification/domain/params/business_verification_request.dart';
import 'package:td_procurement/core/config/app_config/app_config.dart';

class BusinessDataSourceImplementation extends BusinessDataSource {
  final TdApiClient _apiClient;
  final AppConfig _config;
  BusinessDataSourceImplementation(this._apiClient, this._config);

  @override
  Future<RetailOutlet> updateBusinessDetails(
      BusinessVerificationRequest params) async {
    var data = params.toJson();
    data['isAdvance'] = true;
    final res = await _apiClient.post(
      '${_config.consoleUrl}/api/v3/procurement/key_account/edit',
      data: data,
    );
    return RetailOutlet.fromMap(res.data['data']);
  }

  @override
  Future<InitiateStripeResponse> initiateStripeVerification(
      InitiateStripeRequest request) async {
    final response = await _apiClient.post(
      '${_config.awsApiUrlV2}/create-verification-session',
      data: request.toJson(),
    );

    return InitiateStripeResponse.fromJson(response.data['data']);
  }

  @override
  Future<BankData> getBankList() async {
    final response = await _apiClient.get(
      '${_config.awsApiUrlV2}/list-banks',
    );

    return BankData.fromJson(response.data);
  }
}

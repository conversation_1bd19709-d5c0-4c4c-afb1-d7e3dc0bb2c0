import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:td_commons_flutter/models/order_confirmation.dart';
import 'package:td_commons_flutter/models/order_preview_detail.dart';
import 'package:td_procurement/app/order/cart_notifier.dart';
import 'package:td_procurement/app/order/order_params.dart';
import 'package:td_procurement/app/order/order_use_cases.dart';
import 'package:td_procurement/app/order/order_utils.dart';
import 'package:td_procurement/app/order/screens/index.dart';
import 'package:td_procurement/app/order/widgets/index.dart';
import 'package:td_procurement/core/router/routes.dart';
import 'package:td_procurement/src/components/toast/toast.dart';
import 'package:td_procurement/src/components/widgets/multi_value_listenable_builder.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';
import 'package:td_procurement/src/utils/helpers/methods.dart';
import 'package:td_procurement/src/utils/helpers/providers.dart';

class CreateSalesOrderScreen extends ConsumerStatefulWidget {
  const CreateSalesOrderScreen({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _CreateSalesOrderScreenState();
}

class _CreateSalesOrderScreenState
    extends ConsumerState<CreateSalesOrderScreen> {
  final rightButton1 = ValueNotifier<bool>(false);
  final rightButton2 = ValueNotifier<bool>(false);

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: true,
      onPopInvokedWithResult: (_, __) {
        Future.microtask(() {
          resetCart(ref);
          resetSalesOrderCustomer(ref);
        });
      },
      child: GestureDetector(
        // reset focused filed on export cart items
        onTap: () => ref.read(focusedFieldProvider.notifier).state = null,
        child: Scaffold(
          body: Column(
            children: [
              MultiValueListenableBuilder<bool, bool, bool>(
                valueListenable1: rightButton1,
                valueListenable2: rightButton2,
                builder: (context, loading1, loading2, _, __) {
                  return OrderActionBarWidget(
                    leftText: 'Create Order',
                    leftIconAction: () => context.pop(),
                    rightButton2Text: 'Create Order',
                    rightButton2Loading: loading2,
                    rightButton2Action: () => handleCreateOrder(false, false),
                  );
                },
              ),
              Expanded(
                child: CustomScrollView(
                  slivers: [
                    SliverFillRemaining(
                      hasScrollBody: false,
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Expanded(
                            child: RaiseSalesOrderWidget(),
                          ),
                          Expanded(
                            child: Container(
                              color: Palette.kFCFCFC,
                              child: const PurchaseOrderWidget(OrderType.sales),
                            ),
                          ),
                        ],
                      ),
                    )
                  ],
                ),
              )
            ],
          ),
        ),
      ),
    );
  }

  Future<void> createExportOrder(bool isDraftOrder) async {
    final previewDetail = ref.read(orderPreviewDetailProvider);

    if (previewDetail == null) return;

    setLoading(isDraftOrder);

    final params = CreateOrderParams(orders: previewDetail.rawData!);
    final res = await ref.read(createExportOrderUseCaseProvider(params));

    res.when(
      success: (_) {
        clearLoading(isDraftOrder);
        context.goNamed(kOrdersRoute, extra: true);
        Toast.success('Created your order successfully', context);
        resetCart(ref);
      },
      failure: (error, _) {
        clearLoading(isDraftOrder);
        Toast.apiError(error, context, title: 'Error Creating Orders');
      },
    );
  }

  Future<void> handleCreateOrder(bool isDraftOrder, bool updatingDraft) async {
    final cartState = ref.read(cartProvider);
    final selectedSalesCustomer = ref.read(selectedSalesCustomerProvider);

    if (selectedSalesCustomer == null) {
      return Toast.error(
          'You need to select an outlet before creating an order', context,
          title: 'Cannot create sales order');
    }

    if (cartState.uniqueCartItemsWithNonZeroCount.isEmpty) {
      return Toast.error(
          'You must select an item before creating order', context,
          title: 'Cannot create sales order');
    }

    //?TODO:  confirm the need to get user phone number

    // set loading
    setLoading(isDraftOrder);

    final preparedOrders = await prepareOrder(context, ref, false, null, null);

    if (preparedOrders == null) {
      // clear loading
      clearLoading(isDraftOrder);
      return;
    }

    final fulfilledOrders = getFulfilledOrders(preparedOrders);
    final unfulfilledOrders = getUnFulfilledOrders(preparedOrders);

    // final orderConfirmation = await ref.read(countryTypeProvider).when(
    //     export: () async =>
    //         await handleShippingRates(preparedOrders, cartState.branch!.id),
    //     nonExport: () => null);

    // clear loading
    clearLoading(isDraftOrder);

    if (mounted) {
      return showCustomGeneralDialog(
        context,
        dismissible: false,
        percentage: 0.40,
        child: PreviewOrderScreen(
          isDraftOrigin: false,
          preparedOrder: preparedOrders,
          fulfilledOrders: fulfilledOrders,
          unfulfilledOrders: unfulfilledOrders,
          isDraftOrder: isDraftOrder,
          updatingDraftOrder: updatingDraft,
          // orderConfirmation: orderConfirmation,
          isSalesOrderOrigin: true,
        ),
      );
    }

    //?TODO: confirm the need to verify-checkout for NG retailer who is not credit enabled

    // final Map<String, dynamic> doc = {};
    // List<dynamic> unFulfillableOrders = [];

    // final countryCode = ref.read(countryCodeProvider);
    // if (countryCode == kNgCountryCode) {
    //   unFulfillableOrders = getUnFulfilledOrders(preparedOrders);
    //   final fulfillableOrders = getFulfilledOrders(preparedOrders);
    // }

    // if (countryCode == kGbCountryCode) {
    //   final shippingRates =
    //       await handleShippingRates(preparedOrders, cartState.branch!.id);

    //   if (shippingRates == null) {
    //     // clear loading
    //     rightButton1.value = false;
    //     return;
    //   }
    // }
  }

  void setLoading(bool isDraftOrder) {
    if (isDraftOrder) {
      rightButton1.value = true;
    } else {
      rightButton2.value = true;
    }
  }

  void clearLoading(bool isDraftOrder) {
    if (isDraftOrder) {
      rightButton1.value = false;
    } else {
      rightButton2.value = false;
    }
  }

  Future<OrderConfirmation?> handleShippingRates(
      OrderPreviewDetail orderPreviewDetail, String outletId) async {
    final fulFillableOrders = getFulfilledOrders(orderPreviewDetail);

    if (fulFillableOrders.isEmpty) {
      Toast.error('Cannot create orders with unfulfillable items', context,
          title: 'Error Creating Orders');
      return null;
    }

    final params = ShippingRateParams(orderPreviewDetail.rawData!, outletId);
    final res = await ref.read(fetchShippingRateUseCaseProvider(params));
    return res.when(
      success: (data) => data,
      failure: (e, _) {
        Toast.apiError(e, context);
        return null;
      },
    );
  }
}

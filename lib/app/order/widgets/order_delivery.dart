import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:td_commons_flutter/models/order_item.dart';
import 'package:td_commons_flutter/models/orders.dart';
import 'package:td_procurement/app/order/order_use_cases.dart';
import 'package:td_procurement/app/order/widgets/index.dart';
import 'package:td_procurement/app/shipments/widgets/tile.dart';
import 'package:td_procurement/src/components/buttons/custom_filled_button.dart';
import 'package:td_procurement/src/components/toast/toast.dart';
import 'package:td_procurement/src/components/widgets/index.dart';
import 'package:td_procurement/src/extensions/date_time.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';
import 'package:td_procurement/src/res/values/app_values/app_values.dart';
import 'package:td_procurement/src/res/values/constants/constants.dart';

class OrderDeliveryWidget extends ConsumerStatefulWidget {
  final Order order;
  final VoidCallback onSuccess;

  const OrderDeliveryWidget(
    this.order, {
    super.key,
    required this.onSuccess,
  });

  @override
  ConsumerState<OrderDeliveryWidget> createState() =>
      _OrderDeliveryWidgetState();
}

class _OrderDeliveryWidgetState extends ConsumerState<OrderDeliveryWidget> {
  final loader = ValueNotifier<bool>(false);
  final quantityNotifiers = <String, ValueNotifier<num>>{};

  Order get order => widget.order;
  List<OrderItem> get items => order.items ?? [];

  final String quantityShippedKey = 'quantityShipped';

  late List<Map<String, dynamic>> markedItems;

  @override
  void initState() {
    super.initState();
    _initializeMarkedItems();
  }

  void _initializeMarkedItems() {
    markedItems = items
        .map((x) => {
              ...x.toMap(),
              quantityShippedKey: x.quantity ?? 0,
            })
        .map((map) {
      map.removeWhere(
          (key, value) => value == null || (value is List && value.isEmpty));
      if (map.containsKey('id')) {
        map['_id'] = map.remove('id');
      }
      return map;
    }).toList();

    for (final item in markedItems) {
      final id = item['_id'] as String;
      quantityNotifiers[id] = ValueNotifier<num>(item[quantityShippedKey] ?? 0);
    }
  }

  void _updateMarkedItemQuantity(String id, num newQuantity) {
    final itemIndex = markedItems.indexWhere((item) => item['_id'] == id);
    if (itemIndex != -1) {
      markedItems[itemIndex][quantityShippedKey] = newQuantity;
      quantityNotifiers[id]?.value = newQuantity;
    }
  }

  bool _hasShippedQuantity() {
    return markedItems.any((item) => item[quantityShippedKey] > 0);
  }

  Future<void> _markAsDelivered(BuildContext context) async {
    if (!_hasShippedQuantity()) {
      Toast.error('Input the delivered quantity to ship this order', context,
          title: 'Quantity Required');
      return;
    }

    loader.value = true;

    final res = await ref.read(
      markOrderAsDeliveredUseCaseProvider({'items': markedItems}),
    );

    res.when(
      success: (_) {
        loader.value = false;
        context.pop();
        widget.onSuccess.call();
      },
      failure: (error, _) {
        loader.value = false;
        Toast.apiError(
          error,
          context,
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    return Column(
      children: [
        _buildHeader(context, textTheme),
        _buildOrderDetails(context, textTheme),
        Divider(color: Palette.stroke),
        Expanded(
          child: _buildItemReviewSection(context, textTheme),
        ),
      ],
    );
  }

  Widget _buildHeader(BuildContext context, TextTheme textTheme) {
    return Container(
      height: 70,
      padding: const EdgeInsets.symmetric(horizontal: 20),
      decoration: BoxDecoration(
        border: Border.all(color: Palette.stroke),
      ),
      child: Row(
        children: [
          IconButton(
            icon: SvgPicture.asset('$kSvgDir/order/close.svg'),
            onPressed: () => Navigator.pop(context),
          ),
          const Gap(10),
          Flexible(
            child: Text(
              'Orders',
              style: textTheme.bodyLarge?.copyWith(color: Palette.k6B797C),
            ),
          ),
          const Gap(16),
          SvgPicture.asset('$kSvgDir/packs/chevron_right.svg',
              width: 14, height: 12),
          const Gap(10),
          Flexible(
            child: Text(
              order.orderNumber.toString(),
              style: textTheme.bodyLarge?.copyWith(color: Palette.k6B797C),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOrderDetails(BuildContext context, TextTheme textTheme) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 40),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(order.orderNumber.toString(), style: textTheme.headlineSmall),
          const Gap(10),
          Wrap(
            spacing: 20,
            runSpacing: 10,
            children: [
              TileWidget(
                title: 'AMOUNT'.toUpperCase(),
                subtitle: CurrencyWidget.tableString(
                    context,
                    order.currency?.iso ?? defaultCurrency.iso!,
                    order.total ?? 0),
              ),
              TileWidget(
                  title: 'DELIVERING TO'.toUpperCase(),
                  subtitle: order.address?.address1 ?? '_'),
              TileWidget(
                title: 'CREATED ON'.toUpperCase(),
                subtitle: order.createdAt?.toDayMonth() ?? '_',
              ),
              TileWidget(
                title: 'STATUS'.toUpperCase(),
                subtitle: order.shippingStatus,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildItemReviewSection(BuildContext context, TextTheme textTheme) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 40),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Review and confirm items and quantity (${markedItems.length})',
            style: textTheme.headlineSmall,
          ),
          const Gap(20),
          Expanded(
            child: ListView.builder(
              itemCount: markedItems.length,
              itemBuilder: (context, index) {
                final item = markedItems[index];
                return Padding(
                  padding: const EdgeInsets.only(bottom: 12),
                  child: _buildItemTile(context, textTheme, item),
                );
              },
            ),
          ),
          Container(
            padding: const EdgeInsets.only(top: 20),
            child: SizedBox(
              width: double.maxFinite,
              child: CustomFilledButton(
                text: 'Mark as delivered',
                loaderNotifier: loader,
                disabledNotifier: loader,
                onPressed: () => _markAsDelivered(context),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildItemTile(
      BuildContext context, TextTheme textTheme, Map<String, dynamic> item) {
    final itemId = item['_id'] as String;
    final quantityNotifier = quantityNotifiers[itemId];

    if (quantityNotifier == null) {
      return const SizedBox.shrink();
    }

    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Palette.stroke),
        borderRadius: BorderRadius.circular(8),
      ),
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 14),
      width: 546,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ListTile(
            leading: CachedImage(item['variantId'], ImageSize.small),
            title: Text(item['name'], style: textTheme.bodyMedium),
          ),
          const Gap(20),
          Text(
            'Quantity',
            style: textTheme.bodySmall?.copyWith(color: Palette.blackSecondary),
          ),
          const Gap(5),
          ValueListenableBuilder<num>(
            valueListenable: quantityNotifier,
            builder: (context, currentQuantity, child) {
              return GridItemQuantityPicker(
                currentQuantity,
                item['quantity'],
                height: 40,
                allowDecimal: true,
                onQuantityChanged: (quantity) {
                  _updateMarkedItemQuantity(itemId, quantity);
                },
              );
            },
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    for (final notifier in quantityNotifiers.values) {
      notifier.dispose();
    }
    quantityNotifiers.clear();
    loader.dispose();
    super.dispose();
  }
}

import 'dart:math';

import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:td_commons_flutter/models/cart_item.dart';
import 'package:td_commons_flutter/models/order_item.dart';
import 'package:td_commons_flutter/models/order_preview.dart';
import 'package:td_commons_flutter/models/order_preview_detail.dart';
import 'package:td_commons_flutter/models/orders.dart';
import 'package:td_commons_flutter/models/user.dart';
import 'package:td_commons_flutter/models/variant.dart';
import 'package:td_procurement/app/auth/domain/logic/controllers/user_controller.dart';
import 'package:td_procurement/app/order/cart_notifier.dart';
import 'package:td_procurement/app/order/order_state.dart';
import 'package:td_procurement/app/order/order_use_cases.dart';
import 'package:td_procurement/app/order/widgets/outlet_search_auto_complete.dart';
import 'package:td_procurement/core/models/index.dart';
import 'package:td_procurement/src/components/toast/toast.dart';
import 'package:td_procurement/src/res/values/app_values/app_values.dart';
import 'package:td_procurement/src/res/values/constants/constants.dart';
import 'package:td_procurement/src/utils/helpers/providers.dart';

import 'order_params.dart';

String getQueryString(FetchTransactionsParam params) {
  final filters = <String, dynamic>{};

  if (params.orderStatus.isNotEmpty && params.orderStatus != 'all') {
    if (params.orderStatus == 'pending') {
      filters['status'] = 'open';
    } else if (params.orderStatus == 'completed') {
      filters['status'] = 'shipped';
    } else {
      filters['status'] = params.orderStatus;
    }
  }

  if (params.searchText.isNotEmpty) {
    filters['searchText'] = params.searchText;
    if (params.searchText.startsWith('01')) {
      filters['reference'] = params.searchText;
    }
  }

  if (params.selectedDates.length == 2) {
    filters['startDate'] = params.selectedDates[0]?.toIso8601String();
    filters['endDate'] = params.selectedDates[1]
        ?.add(const Duration(
            hours: 23, minutes: 59, seconds: 59, milliseconds: 999))
        .toIso8601String();
  }

  if (params.selectedOutlets.isNotEmpty) {
    filters['branchOutletIds'] = params.selectedOutlets.join(',');
  }

  filters['page'] = params.currentPage.toString();
  filters['perPage'] = params.perPage.toString();

  return Uri(queryParameters: filters).query;
}

String getSalesOrderQueryString(FetchSalesOrdersParams params) {
  final filters = <String, dynamic>{};

  if (params.status.isNotEmpty && params.status != 'all') {
    filters['status'] = params.status;
  }

  if (params.searchText.isNotEmpty) {
    filters['searchText'] = params.searchText;
  }

  if (params.selectedDates.length == 2) {
    filters['startDate'] = params.selectedDates[0]?.toIso8601String();
    filters['endDate'] = params.selectedDates[1]
        ?.add(const Duration(
            hours: 23, minutes: 59, seconds: 59, milliseconds: 999))
        .toIso8601String();
  }

  filters['page'] = params.currentPage.toString();
  filters['perPage'] = params.perPage.toString();

  return Uri(queryParameters: filters).query;
}

Future<PrintOrderData?> processPrintOrder(
    Transaction transaction, WidgetRef ref,
    [List<Order>? orders]) async {
  // If the order is a draft
  if (transaction.isDraft) {
    return _buildDraftOrderData(transaction, orders);
  }

  // If orders are not provided, fetch them
  final fetchedOrders =
      orders ?? await _fetchOrders(ref, transaction.orderNumber ?? '');
  if (fetchedOrders == null) return null;

  // Process order calculations and return the data
  return _buildProcessedOrderData(transaction, fetchedOrders);
}

PrintOrderData? processSalesPrintOrder(WidgetRef ref, Order order) {
  final processedCalculations = processSalesOrderCalculations(order);

  final poOrders = processedCalculations.items
      .map((item) => PrintOrderItem(
            name: item.name,
            price: item.price,
            quantity: item.count,
            currency: item.currency,
            discount: item.discount,
            tax: item.tax,
            total: item.total,
            createdAt: item.createdAt,
          ))
      .toList();

  return PrintOrderData(
    poOrderReference: order.orderNumber.toString(),
    poOrders: poOrders,
    currency: order.currency ?? defaultCurrency,
    status: order.status ?? '',
    approvalStatus: order.approvalStatus,
    shippingStatus: order.shippingStatus,
    paymentStatus: order.paymentStatus,
    poOrderCalculation: PrintOrderCalculation(
      subtotal: order.promoSubTotal ?? 0,
      promoSubtotal: order.promoSubTotal ?? 0,
      promoTotal: order.promoTotal ?? 0,
      promoItemTotal: order.promoItemTotal ?? 0,
      discount: order.discounts ?? 0,
      total: order.total ?? 0,
      amountDue: order.total ?? 0,
      taxes: order.taxes ?? 0,
      shippingFees: processedCalculations.shippingFees,
      quantityShipped: order.totalShippedQuantity,
      shippingAdjustment: order.shippingAdjustment,
    ),
  );
}

Future<List<Order>?> _fetchOrders(WidgetRef ref, String transactionId) async {
  final response =
      await ref.read(fetchOrderDetailsUseCaseProvider).call(transactionId);
  return response.when(
    success: (data) => data.orders,
    failure: (_, __) => null,
  );
}

PrintOrderData _buildDraftOrderData(
    Transaction transaction, List<Order>? orders) {
  // final subtotal = transaction.items?.fold<double>(
  //         0, (acc, item) => acc + (item.price ?? 0) * (item.quantity ?? 0)) ??
  //     0;
  final subtotal = transaction.orderTotal ?? transaction.amount ?? 0;

  final order = orders != null && orders.isNotEmpty ? orders.first : null;

  final quantityShipped = (orders ?? []).fold(
      0.0, (num sum, Order order) => sum + (order.totalShippedQuantity ?? 0));

  final shippingAdjustment = (orders ?? [])
      .fold(0.0, (num sum, Order order) => sum + (order.shippingAdjustment));

  final poOrders = transaction.items
          ?.map((item) => PrintOrderItem(
              name: item.name ?? '',
              price: item.price ?? 0,
              quantity: item.quantity ?? 0,
              currency: transaction.currency ?? defaultCurrency,
              discount: transaction.discounts ?? 0,
              tax: transaction.tax ?? 0,
              total: (item.price ?? 0) * (item.quantity ?? 0),
              createdAt: transaction.createdAt))
          .toList() ??
      [];

  return PrintOrderData(
    poOrderReference: transaction.draftOrderNumber!,
    poOrders: poOrders,
    currency: transaction.currency ?? defaultCurrency,
    status: transaction.status ?? '',
    approvalStatus: order?.approvalStatus,
    shippingStatus: order?.shippingStatus,
    paymentStatus: order?.paymentStatus,
    poOrderCalculation: PrintOrderCalculation(
      subtotal: subtotal,
      promoItemTotal: transaction.itemTotal ?? 0,
      promoSubtotal: subtotal,
      promoTotal: subtotal,
      discount: transaction.discounts ?? 0,
      total: subtotal,
      amountDue: subtotal,
      taxes: transaction.tax ?? 0,
      shippingFees: transaction.shippingCost ?? 0,
      quantityShipped: quantityShipped,
      shippingAdjustment: shippingAdjustment,
    ),
  );
}

PrintOrderData _buildProcessedOrderData(
    Transaction transaction, List<Order> fetchedOrders) {
  final processedCalculations =
      processOrderCalculations(fetchedOrders, transaction);

  final order = fetchedOrders.isNotEmpty ? fetchedOrders.first : null;

  final quantityShipped = fetchedOrders.fold(
      0.0, (num sum, Order order) => sum + (order.totalShippedQuantity ?? 0));

  final shippingAdjustment = fetchedOrders.fold(
      0.0, (num sum, Order order) => sum + (order.shippingAdjustment));

  final poOrders = processedCalculations.items
      .map((item) => PrintOrderItem(
            name: item.name,
            price: item.price,
            quantity: item.count,
            currency: item.currency,
            discount: item.discount,
            tax: item.tax,
            total: item.total,
            createdAt: item.createdAt,
          ))
      .toList();

  return PrintOrderData(
    poOrderReference: transaction.reference ?? '',
    poOrders: poOrders,
    currency: transaction.currency ?? defaultCurrency,
    status: transaction.status ?? '',
    approvalStatus: order?.approvalStatus,
    shippingStatus: order?.shippingStatus,
    paymentStatus: order?.paymentStatus,
    poOrderCalculation: PrintOrderCalculation(
      subtotal: processedCalculations.subtotal,
      promoSubtotal: processedCalculations.promoSubtotal,
      promoTotal: processedCalculations.promoTotal,
      promoItemTotal: transaction.itemTotal ?? 0,
      discount: processedCalculations.discounts,
      total: processedCalculations.total,
      amountDue: processedCalculations.total,
      taxes: processedCalculations.taxes,
      shippingFees: processedCalculations.shippingFees,
      quantityShipped: quantityShipped,
      shippingAdjustment: shippingAdjustment,
    ),
  );
}

OrderCalculationResult processOrderCalculations(
  List<Order> orders,
  Transaction transaction,
) {
  // Filter out cancelled orders if the transaction itself is not cancelled
  final availableOrders = transaction.status != "cancelled"
      ? orders.where((order) => order.status != "cancelled").toList()
      : orders;

  // Calculate items with adjusted pricing and totals
  final items = availableOrders.expand((order) {
    return (order.items ?? []).map((item) {
      // final price = (item.price ?? 0) + (item.promoDiscount ?? 0);
      // final quantity = item.quantity ?? 0;
      // final itemTotal = price * quantity - (item.discount ?? 0);
      final itemTotal = ((item.price ?? 0) * (item.quantity ?? 0));

      return CalculatedItem(
        price: item.price ?? 0,
        count: item.quantity ?? 0,
        discount: item.discount ?? 0,
        total: itemTotal,
        currency: transaction.currency ?? defaultCurrency,
        name: item.name ?? '',
        tax: 0,
        createdAt: order.createdAt,
        promoSubtotal: order.promoSubTotal ?? 0,
        promoTotal: order.promoTotal ?? 0,
        promoItemTotal: order.promoItemTotal ?? 0,
      );
    });
  }).toList();

  // Calculate aggregate values
  final discounts = availableOrders.fold<num>(
      0, (sum, order) => sum + (order.discounts ?? 0));

  final shippingFees = availableOrders.fold<num>(
      0, (sum, order) => sum + (order.shippingCosts ?? 0));

  final taxes =
      availableOrders.fold<num>(0, (sum, order) => sum + (order.taxes ?? 0));

  final promoSubtotal = availableOrders.fold<num>(
      0, (sum, order) => sum + (order.promoSubTotal ?? 0));

  final promoTotal = availableOrders.fold<num>(
      0, (sum, order) => sum + (order.promoTotal ?? 0));

  final promoItemTotal = availableOrders.fold<num>(
      0, (sum, order) => sum + (order.promoItemTotal ?? 0));

  // Calculate subtotal based on transaction amount minus fees and taxes
  final subtotal = (transaction.amount ?? 0) - (taxes + shippingFees);

  return OrderCalculationResult(
    items: items,
    discounts: discounts,
    taxes: taxes,
    total: transaction.amount ?? 0,
    shippingFees: shippingFees,
    subtotal: subtotal,
    promoSubtotal: promoSubtotal,
    promoTotal: promoTotal,
    promoItemTotal: promoItemTotal,
  );
}

OrderCalculationResult processSalesOrderCalculations(Order order) {
  // Calculate items with adjusted pricing and totals
  final items = (order.items ?? []).map((item) {
    // final price = (item.price ?? 0) + (item.promoDiscount ?? 0);
    return CalculatedItem(
      price: item.price ?? 0,
      count: item.quantity ?? 0,
      discount: item.discount ?? 0,
      total: item.total,
      currency: order.currency ?? defaultCurrency,
      name: item.name ?? '',
      tax: item.tax ?? 0,
      createdAt: order.createdAt,
      shippingFee: order.shippingCosts,
      promoSubtotal: order.promoSubTotal ?? 0,
      promoTotal: order.promoTotal ?? 0,
      promoItemTotal: order.promoItemTotal ?? 0,
    );
  }).toList();

  // Calculate aggregate values
  final discounts = items.fold<num>(0, (sum, order) => sum + (order.discount));
  final shippingFees =
      items.fold<num>(0, (sum, order) => sum + (order.shippingFee ?? 0));

  final taxes = items.fold<num>(0, (sum, order) => sum + (order.tax));

  final promoSubtotal =
      items.fold<num>(0, (sum, order) => sum + order.promoSubtotal);

  final promoTotal = items.fold<num>(0, (sum, order) => sum + order.promoTotal);

  final promoItemTotal =
      items.fold<num>(0, (sum, order) => sum + (order.promoItemTotal));

  // Calculate subtotal based on transaction amount minus fees and taxes
  final subtotal = (order.total ?? 0) - (taxes + shippingFees);

  return OrderCalculationResult(
    items: items,
    discounts: discounts,
    taxes: taxes,
    total: order.total ?? 0,
    shippingFees: shippingFees,
    subtotal: subtotal,
    promoSubtotal: promoSubtotal,
    promoTotal: promoTotal,
    promoItemTotal: promoItemTotal,
  );
}

OrderPrepareError? getOrderPrepareErrorByTag(String tag) {
  return orderPrepareError.firstWhereOrNull(
    (error) => error.tag == tag,
  );
}

String getRandomAlphanumericNumbers([int length = 8]) {
  if (length < 6) {
    throw ArgumentError(
        'Length must be at least 6 to include 3 letters and 3 numbers');
  }

  const String letters = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
  const String numbers = '0123456789';
  final Random random = Random();

  // Generate at least 3 letters
  List<String> characters = List.generate(
    3,
    (index) => letters[random.nextInt(letters.length)],
  );

  // Generate at least 3 numbers
  characters.addAll(
    List.generate(
      3,
      (index) => numbers[random.nextInt(numbers.length)],
    ),
  );

  // Generate the remaining characters randomly from both letters and numbers
  const String allChars = '$letters$numbers';
  characters.addAll(
    List.generate(
      length - 6,
      (index) => allChars[random.nextInt(allChars.length)],
    ),
  );

  // Shuffle the list to mix the characters randomly
  characters.shuffle(random);

  // Join the characters into a string
  return characters.join('');
}

List<OrderPreview> getFulfilledOrders(OrderPreviewDetail orderPreviewDetail) {
  return orderPreviewDetail.orders!
      .where((orderPreview) => orderPreview.metadata?.fulfillable == true)
      .toList();
}

List<OrderPreview> getUnFulfilledOrders(OrderPreviewDetail orderPreviewDetail) {
  final unfulfilledOrders = orderPreviewDetail.orders!
      .where((orderPreview) => orderPreview.metadata?.fulfillable == false)
      .toList();
  return unfulfilledOrders;
}

List<OrderItem> getNonNullOrderItems(List<OrderPreview> orderPreviews) {
  return orderPreviews
      .where((preview) => preview.items != null)
      .expand((preview) => preview.items!)
      .toList();
}

Future<OrderPreviewDetail?> prepareOrder(
    BuildContext context,
    WidgetRef ref,
    bool isDraftOrder,
    bool? useRawOrder,
    OrderPreviewDetail? orderPrepared) async {
  final cartState = ref.read(cartProvider);
  final selectedOutlet = ref.read(selectedSalesCustomerProvider);
  final user = ref.read(userControllerProvider);

  PrepareOrderParams params = PrepareOrderParams(
    outletId: cartState.branch?.id ?? selectedOutlet?.id ?? '',
    cartItems: cartState.uniqueCartItemsWithNonZeroCount,
    contactPhone:
        cartState.branch?.contactPhone ?? selectedOutlet?.contactPhone ?? '',
    isExportCountry: ref.read(countryTypeProvider) == CountryType.export,
  );

  // Set params based on useRawOrder
  params = _setParamsBasedOnOrderType(
      useRawOrder, orderPrepared, params, cartState, user);

  // Add note if available
  if (cartState.note != null &&
      cartState.note!.isNotEmpty &&
      useRawOrder == null) {
    params = params.copyWith(note: {
      'userId': user?.id ?? user?.userId ?? '',
      'body': cartState.note!,
    });
  }

  // Handle draft order scenario
  if (isDraftOrder) {
    //final draftOrderNumber = ref.read(cartProvider.notifier).getReference;
    params = params.copyWith(draftOrderId: cartState.transaction!.id);
  }

  // Prepare the order using the use case provider
  final res = await ref.read(prepareNonExportOrderUseCaseProvider(params));

  // Process result and handle errors
  return res.when(
    success: (result) => result,
    failure: (e, _) => _handleFailure(context, e),
  );
}

// Extracted method for setting params based on order type
PrepareOrderParams _setParamsBasedOnOrderType(
  bool? useRawOrder,
  OrderPreviewDetail? orderPrepared,
  PrepareOrderParams params,
  CartState cartState,
  User? user,
) {
  if (useRawOrder == true && orderPrepared != null) {
    return params.copyWith(rawOrder: orderPrepared.rawData!['rawOrder']);
  }

  if (useRawOrder == false && orderPrepared != null) {
    final fulfilledOrders = getFulfilledOrders(orderPrepared);
    final orderItems = fulfilledOrders
        .expand((e) => e.items ?? [])
        .whereType<OrderItem>()
        .toList();
    return params.copyWith(
      outletId: cartState.branch?.id ?? '',
      orderItems: orderItems,
      contactPhone: cartState.branch?.contactPhone ?? '',
    );
  }

  return params;
}

// // Handle success response
// OrderPreviewDetail? _handleSuccess(Map<String, dynamic> result) {
//   if (result['status'] == 'success') {
//     Map<String, dynamic> data = result['data'];
//     return OrderPreviewDetail.fromMap(data);
//   }
//   return null;
// }

// Handle failure response with ToastBox error handling
OrderPreviewDetail? _handleFailure(BuildContext context, dynamic e) {
  Toast.apiError(e, context, title: 'Error preparing orders');
  return null;
}

/// Updates a cart item with its corresponding `extVariant` details, ensuring the item
/// remains unique in the cart.
///
/// This function is particularly useful for export variants, where the same product
/// can appear multiple times in the cart with different suppliers. It ensures that
/// the uniqueness of each cart item is maintained by overriding the `supplierId` of
/// the `variant` to match the item's supplier while retaining all other properties.
void updateCart(
    WidgetRef ref, OrderState newState, CartItem item, bool isExportCountry) {
  // Fetch the matching variant for the given item from the state
  final variant = getVariantFromCollection(
      item.variant.variantId ?? '', newState, isExportCountry);

  if (variant != null) {
    // Schedule the update to occur after the current frame to avoid UI inconsistencies
    Future.microtask(() {
      ref.read(cartProvider.notifier).addItem(
            item.copyWith(
              // Override the supplierId to ensure the item's uniqueness in the cart
              // and prevent false updates on items not meant to be updated
              variant: variant.copyWith(supplierId: item.variant.supplierId),
            ),
          );
    });
  }
}

Variant? getVariantFromList(WidgetRef ref, String variantSupplierId,
    OrderState orderState, bool isExportCountry, bool isSalesOrderOrigin) {
  if (isSalesOrderOrigin) {
    final supplierVariants = ref.read(supplierInventoryProvider);
    if (supplierVariants is AsyncData && supplierVariants.value!.isNotEmpty) {
      return supplierVariants.value!
          .firstWhereOrNull((e) => e.variantSupplierId == variantSupplierId);
    }
  } else {
    return getVariantFromCollection(
        variantSupplierId, orderState, isExportCountry);
  }
  return null;
}

Variant? getVariantFromCollection(
    String variantSupplierId, OrderState orderState, bool isExportCountry) {
  if (isExportCountry) {
    if (orderState.exportVariants.hasValue &&
        orderState.exportVariants.value!.isNotEmpty) {
      final exportVariants = orderState.exportVariants.value ?? [];
      return exportVariants
          .firstWhereOrNull((e) => e.variantSupplierId == variantSupplierId);
    }
  } else {
    if ((orderState.collections.hasValue &&
            orderState.collections.value!.isNotEmpty) ||
        (orderState.exclusiveCollections.hasValue &&
            orderState.exclusiveCollections.value!.isNotEmpty)) {
      final variantsInCollections = orderState.variantsInCollections;
      final outletVariants = (orderState.outletVariants.hasValue &&
              orderState.outletVariants.value is List)
          ? (orderState.outletVariants.value ?? [])
          : [];
      final variants = [...variantsInCollections, ...outletVariants];

      return variants
          .firstWhereOrNull((e) => e.variantSupplierId == variantSupplierId);
    }
  }

  return null;
}

// Future<OrderPreviewDetail?> prepareOrder(
//     BuildContext context,
//     WidgetRef ref,
//     bool isDraftOrder,
//     bool? useRawOrder,
//     OrderPreviewDetail? orderPrepared) async {
//   final cartState = ref.read(cartProvider);
//   final user = ref.read(userControllerProvider);

//   var params = PrepareOrderParams(
//     outletId: cartState.branch!.id,
//     cartItems: cartState.cartItems,
//     contactPhone: cartState.branch!.contactPhone!,
//   );

//   if (useRawOrder == true) {
//     params = PrepareOrderParams(rawOrder: orderPrepared!.rawData!['rawOrder']);
//   }

//   if (useRawOrder == false) {
//     final fulfilledOrders = getFulfilledOrders(orderPrepared!);
//     final orderItems = fulfilledOrders
//         .expand((e) => e.items ?? [])
//         .whereType<OrderItem>()
//         .toList();
//     params = PrepareOrderParams(
//       outletId: cartState.branch!.id,
//       orderItems: orderItems,
//       contactPhone: cartState.branch!.contactPhone!,
//     );
//   }

//   if (cartState.note != null &&
//       cartState.note!.isNotEmpty &&
//       useRawOrder == null) {
//     params = params.copyWith(note: {
//       'userId': user?.id ?? user?.userId ?? '',
//       'body': cartState.note!
//     });
//   }

//   if (isDraftOrder) {
//     final draftOrderNumber = ref.read(cartProvider.notifier).getReference;
//     params = params.copyWith(draftOrderId: draftOrderNumber);
//   }

//   final res = await ref.read(prepareOrderUseCaseProvider(params));

//   return res.when(
//     success: (result) {
//       if (result['status'] == 'success') {
//         Map<String, dynamic> data = result['data'];
//         return OrderPreviewDetail.fromMap(data);
//       }
//       return null;
//     },
//     failure: (e, _) {
//       ToastBox.apiError(context, e, title: 'Error preparing orders');
//       return null;
//     },
//   );
// }

/// Resets the cart state, including clearing selected branch and resetting order details.
///
/// [clearSelectedBranch] determines if the selected branch should be cleared. Default is true.
/// [forceClear] controls whether to clear the cart aggressively. Default is true.
void resetCart(WidgetRef ref,
    {bool clearSelectedBranch = true, bool forceClear = true}) {
  if (forceClear) {
    ref.read(cartProvider.notifier).clearCart(clearSelectedBranch);
  }

  ref.read(editingCartProvider.notifier).state = [];

  ref.read(orderPreviewDetailProvider.notifier).state = null;
}

void resetSalesOrderCustomer(WidgetRef ref) {
  // reset the sales-order flow selected customer
  ref.read(selectedSalesCustomerProvider.notifier).state = null;
  //reset the supplier items for the selected customer
  ref.read(supplierInventoryProvider.notifier).state = const AsyncData([]);
}

num supplierPrice(dynamic ref, Variant variant) {
  final price = variant.vatPrice ?? variant.price;

  if (price != null) {
    return price;
  }

  final suppliers = variant.suppliers;

  final incoterm = ref
      .read(userControllerProvider)
      ?.currentRetailOutlet
      ?.incoterms
      ?.toLowerCase();

  final targetField = incoterm == 'exw'
      ? 'exw'
      : incoterm == 'fob'
          ? 'fob'
          : null;

  if (targetField == null) return 0;

  final found =
      suppliers.firstWhereOrNull((x) => x.toMap()[targetField] != null);

  return found?.toMap()[targetField] ?? 0;
}

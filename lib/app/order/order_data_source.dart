import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:td_commons_flutter/models/collection.dart';
import 'package:td_commons_flutter/models/driver.dart';
import 'package:td_commons_flutter/models/order_confirmation.dart';
import 'package:td_commons_flutter/models/order_preview_detail.dart';
import 'package:td_commons_flutter/models/orders.dart';
import 'package:td_commons_flutter/models/retailer.dart';
import 'package:td_commons_flutter/models/shipping_details.dart';
import 'package:td_commons_flutter/models/user.dart';
import 'package:td_commons_flutter/models/variant.dart';
import 'package:td_flutter_core/td_flutter_core.dart';
import 'package:td_procurement/app/order/order_utils.dart';
import 'package:td_procurement/app/order/widgets/add_customer.dart';
import 'package:td_procurement/core/DI/di_providers.dart';
import 'package:td_procurement/core/config/app_config/app_config.dart';
import 'package:td_procurement/core/models/index.dart';
import 'package:td_procurement/core/services/api/api_paths.dart';

import 'order_params.dart';

final orderDataSourceProvider = Provider.autoDispose<OrderDataSource>((ref) {
  final apiClient = ref.read(apiClientProvider);
  final config = ref.read(appConfigProvider);

  return OrderDataSourceImplementation(apiClient, config);
});

abstract class OrderDataSource {
  Future<FetchTransactionsResponse> fetchOrders(FetchTransactionsParam params);
  Future<OrderDetails> fetchOrderDetails(String orderId);
  Future<ShipmentResult> fetchShipment(String shipmentId);
  Future<void> deleteOrder(String transactionId, String outletId);
  Future<List<Variant>> fetchExportVariants();
  Future<List<Collection>> fetchCollections(String hexCode, bool exclusive);
  Future<List<Variant>> fetchOutletVariants();
  Future<List<Variant>> searchOutletVariants(String hexCode, String searchTerm);
  Future<OrderPreviewDetail> prepareNonExportOrder(PrepareOrderParams params);
  Future<OrderPreviewDetail> prepareExportOrder(PrepareOrderParams params);
  Future<OrderConfirmation> fetchShippingRate(ShippingRateParams params);
  Future<FetchSalesLocationDriversResponse> fetchSalesLocationDrivers(
      FetchSalesLocationDriverParams params);
  Future<dynamic> assignDriverToOrders(AssignOrdersParams params);

  /// takes the branch's `retailOutletId`
  Future<void> createDraftOrder(String outletId);
  Future<void> updateDraftOrder(UpdateDraftParams params);

  /// Returns the order `reference`
  Future<String> createNonExportCart(CreateOrderParams params);
  Future<CreateOrderResponse> createNonExportOrder(CreateOrderParams params);
  Future<RetailOutlet> getRetailOutlet(String outletId);
  Future<void> chargeOrder(ChargeOrderParams params);

  Future<dynamic> createExportOrder(CreateOrderParams params);
  Future<dynamic> createSalesOrder(CreateOrderParams params);
  Future<FetchSalesOrdersResponse> fetchSalesOrders(
      FetchSalesOrdersParams params);
  Future<Order> getSalesOrder(String orderId);
  Future<List<RetailOutlet>> searchCustomers(String phoneNumber);
  Future<List<Variant>> fetchSupplierInventory(String outletId);
  Future<dynamic> acceptOrder(String reference);
  Future<dynamic> markOrderAsDelivered(Map<String, dynamic> items);
  Future<RetailOutlet> addNewCustomer(CustomerParams params);
}

class OrderDataSourceImplementation implements OrderDataSource {
  final TdApiClient _apiClient;
  final AppConfig _config;

  OrderDataSourceImplementation(this._apiClient, this._config);

  @override
  Future<FetchTransactionsResponse> fetchOrders(
      FetchTransactionsParam params) async {
    final queryString = getQueryString(params);

    final res = await _apiClient
        .get(kTransactionApiPath.replaceFirst(':queryString', queryString));

    final data = res.data['data'];
    final List<dynamic> transactions = data['transactions'];
    final Map<String, dynamic> pagination = data['pagination'];

    return FetchTransactionsResponse(
      transactions: transactions.map((x) => Transaction.fromMap(x)).toList(),
      queryParams: QueryParameters.fromMap(pagination),
    );
  }

  @override
  Future<OrderDetails> fetchOrderDetails(String orderId) async {
    final res = await _apiClient
        .get(kOrderDetailsApiPath.replaceFirst(':orderId', orderId));
    return OrderDetails.fromMap(res.data['data']);
  }

  @override
  Future<ShipmentResult> fetchShipment(String shipmentId) async {
    final res = await _apiClient.get(
        '${_config.consoleUrl}${kShipmentApiPath.replaceFirst(':shipmentId', shipmentId)}');
    return ShipmentResult.fromMap(res.data['data']);
  }

  @override
  Future<void> deleteOrder(String transactionId, String outletId) async {
    await _apiClient.delete('${_config.awsApiUrlV2}$kDraftOrderApiPath',
        data: {'_id': transactionId, 'retailOutletId': outletId});
    return;
  }

  @override
  Future<List<Variant>> fetchExportVariants() async {
    final res =
        await _apiClient.get('${_config.awsApiUrlV3}$kExportVariantsApiPath');

    final body = res.data?['body'];

    if (body is Map && body['exportVariants'] is List) {
      final variants = body['exportVariants'] as List<dynamic>;
      return variants.map((x) => Variant.fromMap(x)).toList();
    }

    return [];
  }

  @override
  Future<List<Collection>> fetchCollections(
      String hexCode, bool exclusive) async {
    late Response<dynamic> res;

    if (exclusive) {
      res = await _apiClient.get(
          '${_config.firebaseServiceUrl}${kCollectionApiPath.replaceFirst(':hexCode', hexCode)}&collectionType=exclusive');
    } else {
      res = await _apiClient.get(
          '${_config.firebaseServiceUrl}${kCollectionApiPath.replaceFirst(':hexCode', hexCode)}&collectionType=nonExclusive');
    }

    final List<dynamic> data = res.data['collection'];
    return data.map((x) => Collection.fromMap(x)).toList();
  }

  @override
  Future<List<Variant>> fetchOutletVariants() async {
    final res =
        await _apiClient.get('${_config.awsApiUrlV3}$kOutletVariantsApiPath');
    final List<dynamic> data = res.data['body'];
    return data.map((x) => Variant.fromMap(x)).toList();
  }

  @override
  Future<List<Variant>> searchOutletVariants(
      String hexCode, String searchTerm) async {
    final res = await _apiClient.get(
        '${_config.awsApiUrlV3}${kVariantSearchApiPath.replaceFirst(':hexCode', hexCode)}'
            .replaceFirst(':searchTerm', searchTerm));
    final List<dynamic> data = res.data['body'];
    return data.map((x) => Variant.fromMap(x)).toList();
  }

  @override
  Future<OrderPreviewDetail> prepareNonExportOrder(
      PrepareOrderParams params) async {
    final res = await _apiClient.post(kPrepareNonExportOrderApiPath,
        data: params.toMap());
    return OrderPreviewDetail.fromMap(res.data['data']);
  }

  @override
  Future<OrderPreviewDetail> prepareExportOrder(
      PrepareOrderParams params) async {
    final res =
        await _apiClient.post(kPrepareExportOrderApiPath, data: params.toMap());
    return OrderPreviewDetail.fromMap(res.data['data']);
  }

  @override
  Future<OrderConfirmation> fetchShippingRate(ShippingRateParams params) async {
    final res = await _apiClient.post('${_config.appUrl}$kShippingRateApiPath',
        data: params.toMap());

    final Map<String, dynamic> data = res.data['data'];

    final shippingDetails = ShippingDetails.fromMap(data);
    final orderPreviewDetail = OrderPreviewDetail.fromMap(data);
    final fulfilledOrders = getFulfilledOrders(orderPreviewDetail);
    final orderItems = getNonNullOrderItems(fulfilledOrders);

    return OrderConfirmation(
      shippingDetails: shippingDetails,
      orderItems: orderItems,
      shippingDetailsMap: data,
    );
  }

  @override
  Future<void> createDraftOrder(String outletId) async {
    await _apiClient.post('${_config.awsApiUrlV2}$kDraftOrderApiPath',
        data: {'retailOutletId': outletId});
    return;
  }

  @override
  Future<void> updateDraftOrder(UpdateDraftParams params) async {
    await _apiClient.put('${_config.awsApiUrlV2}$kDraftOrderApiPath',
        data: params.toMap());
    return;
  }

  @override
  Future<String> createNonExportCart(CreateOrderParams params) async {
    final res = await _apiClient.post(
        '${_config.awsApiUrlV2}$kCreateCartApiPath',
        data: params.toMap());
    return res.data['data']['cartOrderId'];
  }

  @override
  Future<RetailOutlet> getRetailOutlet(String outletId) async {
    final res = await _apiClient.get(
        '${_config.appUrl}${kOutletApiPath.replaceFirst(':outletId', outletId)}');
    return RetailOutlet.fromMap(res.data['data']);
  }

  @override
  Future<CreateOrderResponse> createNonExportOrder(
      CreateOrderParams params) async {
    final res = await _apiClient
        .post('${_config.awsApiUrl}$kCreateOrderApiPath', data: params.toMap());
    return CreateOrderResponse.fromMap(res.data['data']);
  }

  @override
  Future<void> chargeOrder(ChargeOrderParams params) async {
    await _apiClient.post('${_config.awsApiUrl}$kChargeOrderApiPath',
        data: params.toMap());
    return;
  }

  @override
  Future createExportOrder(CreateOrderParams params) async {
    await _apiClient.post(kCreateExportOrderApiPath, data: params.toMap());
    return;
  }

  @override
  Future<dynamic> createSalesOrder(CreateOrderParams params) async {
    final res =
        await _apiClient.post(kCreateSalesOrderApiPath, data: params.toMap());
    return res;
  }

  @override
  Future<FetchSalesOrdersResponse> fetchSalesOrders(
      FetchSalesOrdersParams params) async {
    final queryString = getSalesOrderQueryString(params);

    final res = await _apiClient.get(
        '${_config.consoleUrl}${kSalesOrdersApiPath.replaceFirst(':queryString', queryString)}');

    final resData = res.data;

    List<dynamic> orders = [];
    Map<String, dynamic> pagination = {};

    if (resData is List) {
      orders = resData;
    } else {
      final data = resData['data'];
      orders = data['orders'];
      pagination = data['pagination'];
    }

    return FetchSalesOrdersResponse(
      orders: orders.map((x) => Order.fromMap(x)).toList(),
      queryParams: QueryParameters.fromMap(pagination),
    );
  }

  @override
  Future<Order> getSalesOrder(String orderId) async {
    final res = await _apiClient.get(
        '${_config.consoleUrl}${kSalesOrderApiPath.replaceFirst(':id', orderId)}');
    return Order.fromMap(res.data['data']['order']);
  }

  @override
  Future<List<RetailOutlet>> searchCustomers(String phoneNumber) async {
    final res = await _apiClient.post('${_config.consoleUrl}$kCustomerApiPath',
        data: {'search': phoneNumber});
    final List<dynamic> data = res.data;
    return data.map((x) => RetailOutlet.fromMap(x)).toList();
  }

  @override
  Future<List<Variant>> fetchSupplierInventory(String outletId) async {
    final res = await _apiClient.get(
        '${_config.consoleUrl}${kSupplierInventoryApiPath.replaceFirst(':outletId', outletId)}');
    final List<dynamic> data = res.data['items'];
    return data.map((x) => Variant.fromMap(x)).toList();
  }

  @override
  Future<dynamic> acceptOrder(String reference) async {
    final res = await _apiClient.post(
        '${_config.consoleUrl}${kOrderAcceptanceApiPath.replaceFirst(':reference', reference)}');
    return res.data;
  }

  @override
  Future<dynamic> markOrderAsDelivered(Map<String, dynamic> items) async {
    final res = await _apiClient
        .put('${_config.consoleUrl}$orderDeliveryApiPath', data: items);
    return res.data;
  }

  @override
  Future<RetailOutlet> addNewCustomer(CustomerParams params) async {
    final res = await _apiClient.post(
      '${_config.firebaseServiceUrl}/shop/v2/signup',
      data: params.toMap(),
    );
    final user = User.fromMap(res.data['data']['details']['account']);
    return user.currentRetailOutlet!;
  }

  @override
  Future<FetchSalesLocationDriversResponse> fetchSalesLocationDrivers(
      FetchSalesLocationDriverParams params) async {
    final res = await _apiClient.get(
        '${_config.consoleUrl}${kSalesLocationDriversApiPath.replaceFirst(':locationId', params.locationId)}');
    final List<dynamic> listData = res.data['data']['drivers'];
    final drivers = listData.map((e) => Driver.fromMap(e)).toList();
    return FetchSalesLocationDriversResponse(drivers: drivers);
  }

  @override
  Future<dynamic> assignDriverToOrders(AssignOrdersParams params) async {
    final body = {
      'assigneeId': params.driverId,
      'orderIds': params.orderIds,
      'createTrip': true,
    };
    final res = await _apiClient.post(
        '${_config.consoleUrl}${kAssignDriverToOrdersApiPath.replaceFirst(':locationId', params.locationId)}',
        data: body);
    return res.data;
  }
}

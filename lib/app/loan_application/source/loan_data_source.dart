import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:td_flutter_core/td_flutter_core.dart';
import 'package:td_procurement/core/DI/di_providers.dart';

abstract class LoanDataSource {
  Future<dynamic> applyForLoan();
}

final loanDataProvider = Provider<LoanDataSource>((ref) {
  return LoanDataSourceImplementation(ref);
});

class LoanDataSourceImplementation extends LoanDataSource {
  final Ref _ref;
  LoanDataSourceImplementation(this._ref);

  late final TdApiClient _apiClient = _ref.read(apiClientProvider);
  late final config = _ref.read(appConfigProvider);

  @override
  Future<dynamic> applyForLoan() async {
    final res = await _apiClient.post(
      "${config.consoleUrl}/api/v3/invoice-discounts/request",
    );

    return res.data;
  }
}

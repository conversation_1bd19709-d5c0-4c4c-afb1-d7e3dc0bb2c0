import 'package:flutter_riverpod/flutter_riverpod.dart';

class LoanStateController extends Notifier<bool> {
  //initial value
  DateTime presentDay = DateTime.now();
  DateTime pastDay = DateTime.now().subtract(const Duration(days: 30));
  String selectedTimeFilter = "Last 30 Days";
  @override
  build() {
    return false;
  }

  updateLoanContractState(bool newState) {
    state = newState;
  }
}

final loanContractProvider = NotifierProvider<LoanStateController, bool>(() {
  return LoanStateController();
});

import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:td_flutter_core/td_flutter_core.dart';
import 'package:td_procurement/app/loan_application/domain/loan_use_cases.dart';
import 'package:td_procurement/app/loan_application/presentation/logic/loan_state.dart';
import 'package:td_procurement/core/router/routes.dart';
import 'package:td_procurement/src/components/buttons/custom_filled_button.dart';
import 'package:td_procurement/src/components/toast/toast.dart';
import 'package:td_procurement/src/utils/exceptions/app_exception.dart';

class LoanInfo extends ConsumerWidget {
  LoanInfo({super.key});

  final List<String> points = const [
    'The scheme allows customers to apply for credit of 1 Million Naira and above',
    'Customers who apply for the scheme automatically consent to and will have to pay for a credit check to be run on their organisation',
    'Every application is subject to approval and the credit limit that is approved might vary depending on the decision of the credit officer',
    'Successful applicants will have their new credit limit assigned to them for a period of 6 months after which it is renewable',
    'A POS machine will be deployed to successful applicants and daily repayments will be deducted from the collections everyday except Sunday',
    'Applicants will be required to pay a caution and delivery fee before the POS is deployed',
    'Standard POS charges of 0.5% capped at N500 will apply to the POS transactions',
    'All collections minus the daily repayment and POS charges will be settled back to the account',
  ];

  final ValueNotifier<bool> _loader = ValueNotifier(false);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        automaticallyImplyLeading: false,
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 1,
        actions: [
          Padding(
            padding: const EdgeInsets.only(right: 20),
            child: InkWell(
              onTap: () {
                context.pop();
              },
              child: const Icon(
                Icons.clear,
                color: Colors.black,
                size: 25,
              ),
            ),
          ),
        ],
      ),
      body: LayoutBuilder(
        builder: (context, constraints) {
          final isWideScreen = constraints.maxWidth > 800;
          return Stack(
            children: [
              SingleChildScrollView(
                padding: EdgeInsets.symmetric(
                  horizontal: isWideScreen ? 80 : 20,
                  vertical: 30,
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Here’s what you need to know about Shop TopUp’s Merchant Cash Advance',
                      style:
                          Theme.of(context).textTheme.headlineSmall?.copyWith(
                                fontWeight: FontWeight.bold,
                              ),
                    ),
                    const SizedBox(height: 30),
                    ...points.map(
                      (point) => Padding(
                        padding: const EdgeInsets.symmetric(vertical: 8.0),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Icon(Icons.check_circle,
                                color: Colors.orange, size: 24),
                            const SizedBox(width: 10),
                            Expanded(
                              child: Text(
                                point,
                                style: const TextStyle(fontSize: 16),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(height: 100), // For bottom spacing
                  ],
                ),
              ),
              Positioned(
                bottom: 0,
                left: 0,
                right: 0,
                child: Container(
                  color: Colors.white,
                  padding:
                      const EdgeInsets.symmetric(vertical: 16, horizontal: 20),
                  child: Center(
                    child: SizedBox(
                      width: isWideScreen ? 300 : double.infinity,
                      height: 40,
                      child: CustomFilledButton(
                        onPressed: () => context.pushNamed(
                            kBusinessVerificationRoute,
                            extra: {'isInvoiceFinanceApplication': true}),
                        text: 'Apply',
                        loaderNotifier: _loader,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }
}

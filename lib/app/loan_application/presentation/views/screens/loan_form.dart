import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:td_procurement/app/business_verification/domain/params/file_upload_params.dart';
import 'package:td_procurement/app/business_verification/presentation/logic/file_upload.dart';
import 'package:td_procurement/src/components/buttons/custom_filled_button.dart';
import 'package:td_procurement/src/components/toast/toast.dart';
import 'package:td_procurement/src/res/assets/svgs/svgs.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';

class LoanForm extends StatefulWidget {
  const LoanForm({super.key});

  @override
  State<LoanForm> createState() => _LoanFormState();
}

class _LoanFormState extends State<LoanForm> with FileUpload {
  late TextEditingController bvnController;
  late TextEditingController rcController;

  Map<String, dynamic> filesMap = {};
  String nationalId = "National Identity Card";
  String certOfInco = "bnCertificate";
  String partOfPartners = "partnerParticulars";

  final ValueNotifier<bool> _loader = ValueNotifier(false);

  fieldTitle(String key) {
    switch (key) {
      case 'National Identity Card':
        return 'National ID e.g driver\'s license,passport';
      case 'bnCertificate':
        return 'Certificate of Incorporation';
      case 'partnerParticulars':
        return 'Particulars of patners';
    }
  }

  @override
  void initState() {
    bvnController = TextEditingController();
    rcController = TextEditingController();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Scaffold(
      appBar: AppBar(
        automaticallyImplyLeading: false,
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 1,
        actions: [
          Padding(
            padding: const EdgeInsets.only(right: 20),
            child: InkWell(
              onTap: () {
                context.pop();
              },
              child: const Icon(
                Icons.clear,
                color: Colors.black,
                size: 25,
              ),
            ),
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.symmetric(vertical: 20),
        child: LayoutBuilder(
          builder: (context, constraints) {
            final isWideScreen = constraints.maxWidth > 800;
            return Center(
              child: SizedBox(
                width: 500,
                child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Loan Information',
                        style:
                            Theme.of(context).textTheme.headlineSmall?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                      ),
                      const Gap(10),
                      Text(
                        'Fill in the following information ',
                        style: theme.textTheme.bodyMedium?.copyWith(
                            fontWeight: FontWeight.w400,
                            color: Palette.blackSecondary),
                      ),
                      const Gap(10),
                      Row(
                        children: [
                          Expanded(
                            child: TextFormField(
                              controller: rcController,
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return "Field required";
                                } else if (value.length < 10) {
                                  return "Minimum number of characters is 10";
                                } else {
                                  return null;
                                }
                              },
                              decoration: const InputDecoration(
                                  hintText: 'Business Registration Number'),
                              keyboardType: TextInputType.emailAddress,
                              //style: ,
                            ),
                          ),
                        ],
                      ),
                      const Gap(10),
                      Row(
                        children: [
                          Expanded(
                            child: TextFormField(
                              controller: bvnController,
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return "Field required";
                                } else if (value.length < 11) {
                                  return "Minimum number of characters is 11";
                                } else {
                                  return null;
                                }
                              },
                              decoration: const InputDecoration(
                                  hintText: 'Account signatory BVN'),
                              keyboardType: TextInputType.emailAddress,
                              onFieldSubmitted: (_) {},
                              //style: ,
                            ),
                          ),
                        ],
                      ),
                      const Gap(10),
                      filesMap[nationalId] != null
                          ? customCardUploaded(filesMap[nationalId], nationalId)
                          : customCard(nationalId),
                      const Gap(10),
                      filesMap[certOfInco] != null
                          ? customCardUploaded(filesMap[certOfInco], certOfInco)
                          : customCard(certOfInco),
                      const Gap(10),
                      Row(children: [
                        Expanded(
                          child: Container(
                            padding: const EdgeInsets.all(8),
                            decoration: BoxDecoration(
                                color: Palette.k202020.withOpacity(.06),
                                borderRadius: BorderRadius.circular(8)),
                            child: const Text(
                                'Only upload .pdf, .jpg or .png file, maximum file size is 1mb'),
                          ),
                        ),
                      ]),
                      const Gap(30),
                      Row(
                        children: [
                          Expanded(
                            child: CustomFilledButton(
                              onPressed: () async {
                                if (filesMap[nationalId] != null &&
                                    filesMap[certOfInco] != null &&
                                    filesMap[partOfPartners] != null) {
                                  _loader.value = true;

                                  return;
                                }
                                Toast.error("Upload all documents", context);
                              },
                              text: 'Finish and Submit',
                              loaderNotifier: _loader,
                            ),
                          ),
                        ],
                      )
                    ]),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget customCard(String title) {
    final theme = Theme.of(context);
    return InkWell(
      onTap: () => fetchDoc(title),
      child: Card(
        color: Palette.kFCFCFC,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Row(
            children: [
              SvgPicture.asset(
                kfileSvg,
                color: Palette.statusPending,
              ),
              const Gap(10),
              Text(
                fieldTitle(title),
                style: theme.textTheme.bodyMedium
                    ?.copyWith(fontWeight: FontWeight.w500),
              )
            ],
          ),
        ),
      ),
    );
  }

  void fetchDoc(String key) async {
    final allowedExtensions = ['pdf', 'jpg', 'png', 'jpeg'];
    FilePickerResult? result = await FilePicker.platform.pickFiles(
      type: FileType.custom,
      allowedExtensions: allowedExtensions,
    );
    if (result != null) {
      PlatformFile file = result.files.single;
      String? ext = file.extension!.toLowerCase();

      if (!allowedExtensions.contains(ext)) {
        Toast.error("Unsupported File format", context);
        return;
      }

      if (!isValidFileSize(file.bytes!, context)) {
        return;
      }

      setState(() {
        filesMap[key] = FileUploadParams(
            file.extension!, file.bytes!, file, file.extension, key);
      });
    }
  }

  Widget customCardUploaded(FileUploadParams params, String key) {
    final theme = Theme.of(context);
    return Card(
      color: theme.scaffoldBackgroundColor,
      child: Container(
        padding: const EdgeInsets.all(12),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              flex: 3,
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                      padding: const EdgeInsets.all(4),
                      color: Palette.statusPending.withOpacity(.1),
                      child: SvgPicture.asset(
                        kfileSvg,
                        color: Palette.statusPending,
                      )),
                  const Gap(5),
                  Expanded(
                      child: Text(
                    params.file.name,
                    style: theme.textTheme.bodySmall,
                    maxLines: 2,
                  ))
                ],
              ),
            ),
            const Gap(10),
            Expanded(
              flex: 2,
              child: Row(
                children: [
                  InkWell(
                    onTap: () {
                      fetchDoc(key);
                    },
                    child: Container(
                        padding: const EdgeInsets.all(4),
                        decoration: BoxDecoration(
                            color: Palette.placeholder.withOpacity(.1),
                            borderRadius: BorderRadius.circular(8)),
                        child: Text('Upload new',
                            style: theme.textTheme.bodySmall
                                ?.copyWith(fontWeight: FontWeight.w600))),
                  ),
                  const Gap(5),
                  InkWell(
                    onTap: () {
                      setState(() {
                        filesMap[params.key!] = null;
                      });
                    },
                    child: Container(
                        padding: const EdgeInsets.all(4),
                        decoration: BoxDecoration(
                            color: Palette.deleteRed.withOpacity(.1),
                            borderRadius: BorderRadius.circular(8)),
                        child: Text('Delete',
                            style: theme.textTheme.bodySmall?.copyWith(
                                fontWeight: FontWeight.w500,
                                color: Palette.deleteRed))),
                  )
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:td_commons_flutter/models/retailer.dart';
import 'package:td_commons_flutter/models/user.dart';
import 'package:td_procurement/app/auth/data/models/outlet_type.dart';
import 'package:td_procurement/app/auth/data/models/user_status.dart';
import 'package:td_procurement/app/auth/data/models/verified_otp.dart';
import 'package:td_procurement/app/auth/domain/params/check_phone.dart';
import 'package:td_procurement/app/auth/domain/params/login_token.dart';
import 'package:td_procurement/app/auth/domain/params/partner_reg.dart';
import 'package:td_procurement/app/auth/domain/params/send_otp.dart';
import 'package:td_procurement/app/auth/domain/params/verify_otp.dart';
import 'package:td_procurement/app/auth/data/repo/auth_repo.dart';
import 'package:td_procurement/core/DI/di_providers.dart';
import 'package:td_procurement/core/services/api/api_response.dart';

final loginWithOtpUseCaseProvider =
    AutoDisposeProviderFamily<Future<ApiResponse<User>>, LoginTokenParams>(
  (ref, arg) => UseCase<User>().call(
    () => ref.read(authRepoProvider).loginWithOtp(arg),
  ),
);

final verifyEmailUseCaseProvider = AutoDisposeProviderFamily<
    Future<ApiResponse<UserStatus>>, CheckPhoneParams>(
  (ref, arg) => UseCase<UserStatus>()(
    () => ref.read(authRepoProvider).verifyEmail(arg),
  ),
);

final sendOtpUseCaseProvider = AutoDisposeProviderFamily<
    Future<ApiResponse<Map<String, dynamic>?>>, SendOTParams>(
  (ref, arg) => UseCase<Map<String, dynamic>?>()(
    () => ref.read(authRepoProvider).sendOTP(arg),
  ),
);

final verifyOtpUseCaseProvider =
    AutoDisposeProviderFamily<Future<ApiResponse<VerifiedOTP>>, VerifyOTParams>(
  (ref, arg) => UseCase<VerifiedOTP>()(
    () => ref.read(authRepoProvider).verifyOTP(arg),
  ),
);

final fetchOutletsUseCaseProvider = AutoDisposeProviderFamily<
    Future<ApiResponse<List<RetailOutletType>>>, String>(
  (ref, arg) => UseCase<List<RetailOutletType>>()(
    () => ref.read(authRepoProvider).fetchOutletTypes(arg),
  ),
);

final signUpUseCaseProvider =
    AutoDisposeProviderFamily<Future<ApiResponse<bool>>, Map>(
  (ref, arg) => UseCase<bool>()(
    () => ref.read(authRepoProvider).signUp(arg),
  ),
);

final registerPartnerUseCaseProvider =
    AutoDisposeProviderFamily<Future<ApiResponse<bool>>, PartnerRegParams>(
  (ref, arg) => UseCase<bool>()(
    () => ref.read(authRepoProvider).registerPartner(arg),
  ),
);

final getRetailOutletUseCaseProvider =
    AutoDisposeProviderFamily<Future<ApiResponse<RetailOutlet>>, User>(
  (ref, arg) => UseCase<RetailOutlet>()(
      () => ref.read(authRepoProvider).getRetailOutlet(arg)),
);

final deleteUserAccountUseCaseProvider =
    AutoDisposeProvider<Future<ApiResponse<bool>>>(
  (ref) => UseCase<bool>()(
    () => ref.read(authRepoProvider).deleteUserAccount(),
  ),
);

final checkLoanStatusUseCaseProvider =
    AutoDisposeProvider<Future<ApiResponse<bool>>>(
  (ref) => UseCase<bool>()(
    () => ref.read(authRepoProvider).checkLoanStatus(),
  ),
);

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:td_commons_flutter/models/retailer.dart';
import 'package:td_commons_flutter/models/user.dart';
import 'package:td_procurement/app/auth/data/source/auth_data_source.dart';
import 'package:td_procurement/app/auth/data/models/outlet_type.dart';
import 'package:td_procurement/app/auth/data/models/user_status.dart';
import 'package:td_procurement/app/auth/data/models/verified_otp.dart';
import 'package:td_procurement/app/auth/domain/params/check_phone.dart';
import 'package:td_procurement/app/auth/domain/params/login_token.dart';
import 'package:td_procurement/app/auth/domain/params/partner_reg.dart';
import 'package:td_procurement/app/auth/domain/params/send_otp.dart';
import 'package:td_procurement/app/auth/domain/params/verify_otp.dart';
import 'package:td_procurement/core/helpers/dio_interceptor.dart';
import 'package:td_procurement/core/services/api/api_response.dart';

abstract class AuthRepo {
  Future<ApiResponse<User>> loginWithOtp(LoginTokenParams params);
  Future<ApiResponse<UserStatus>> verifyEmail(CheckPhoneParams params);
  Future<ApiResponse<Map<String, dynamic>?>> sendOTP(SendOTParams params);
  Future<ApiResponse<VerifiedOTP>> verifyOTP(VerifyOTParams params);
  Future<ApiResponse<List<RetailOutletType>>> fetchOutletTypes(
      String countryIso);
  Future<ApiResponse<bool>> signUp(Map params);
  Future<ApiResponse<bool>> deleteUserAccount();
  Future<ApiResponse<bool>> registerPartner(PartnerRegParams params);
  Future<ApiResponse<RetailOutlet>> getRetailOutlet(User user);
  Future<ApiResponse<bool>> checkLoanStatus();
}

final authRepoProvider = Provider<AuthRepo>((ref) {
  return AuthRepoImplementation(ref);
});

class AuthRepoImplementation extends AuthRepo {
  final Ref _ref;

  AuthRepoImplementation(this._ref);

  late final AuthDataSource _dataSource = _ref.read(authDataProvider);

  @override
  Future<ApiResponse<User>> loginWithOtp(LoginTokenParams params) {
    return dioInterceptor(() => _dataSource.loginWithOtp(params), _ref);
  }

  @override
  Future<ApiResponse<UserStatus>> verifyEmail(CheckPhoneParams params) {
    return dioInterceptor(() => _dataSource.verifyEmailAddress(params), _ref);
  }

  @override
  Future<ApiResponse<Map<String, dynamic>?>> sendOTP(SendOTParams params) {
    return dioInterceptor(() => _dataSource.sendOTP(params), _ref);
  }

  @override
  Future<ApiResponse<VerifiedOTP>> verifyOTP(VerifyOTParams params) {
    return dioInterceptor(() => _dataSource.verifyOTP(params), _ref);
  }

  @override
  Future<ApiResponse<List<RetailOutletType>>> fetchOutletTypes(
      String countryIso) {
    return dioInterceptor(() => _dataSource.fetchOutletTypes(countryIso), _ref);
  }

  @override
  Future<ApiResponse<bool>> signUp(Map params) {
    return dioInterceptor(() => _dataSource.signUp(params), _ref);
  }

  @override
  Future<ApiResponse<bool>> registerPartner(PartnerRegParams params) {
    return dioInterceptor(() => _dataSource.registerPartner(params), _ref);
  }

  @override
  Future<ApiResponse<RetailOutlet>> getRetailOutlet(User user) {
    return dioInterceptor(() => _dataSource.getRetailOutlet(user), _ref);
  }

  @override
  Future<ApiResponse<bool>> deleteUserAccount() {
    return dioInterceptor(() => _dataSource.deleteUserAccount(), _ref);
  }

  @override
  Future<ApiResponse<bool>> checkLoanStatus() {
    return dioInterceptor(
        logoutOn401: false, () => _dataSource.checkLoanContractStatus(), _ref);
  }
}

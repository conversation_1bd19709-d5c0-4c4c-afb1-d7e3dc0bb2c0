import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:td_commons_flutter/models/retailer.dart';
import 'package:td_procurement/app/auth/domain/logic/controllers/user_controller.dart';
import 'package:td_procurement/app/shell/presentation/ui/widgets/log_out_tile.dart';
import 'package:td_procurement/app/shell/presentation/ui/widgets/side_bar_tile.dart';
import 'package:td_procurement/core/router/routes.dart';
import 'package:td_procurement/src/res/assets/svgs/svgs.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';
import 'package:td_procurement/src/res/values/constants/constants.dart';
import 'package:td_procurement/src/utils/helpers/providers.dart';

class SideBar extends ConsumerStatefulWidget {
  const SideBar({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() {
    return _SideBar();
  }
}

class _SideBar extends ConsumerState<SideBar>
    with AutomaticKeepAliveClientMixin {
  late GoRouter _router;
  String get location => _router.routerDelegate.currentConfiguration.fullPath;

  bool _isActiveRoute(SideBarItem item) {
    return location.startsWith("/${item.route.toLowerCase()}");
  }

  late CountryType countryType = ref.read(countryTypeProvider);
  late String? customerGroup =
      ref.read(userControllerProvider)?.currentRetailOutlet?.customerGroup;
  late bool isExportCountry = countryType == CountryType.export;

  final ScrollController _scrollController = ScrollController();
  final _listKey = GlobalKey();
  late List<SideBarItem> sideBarItems;
  bool _isInitialRoute = true;

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(() {});
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _router = GoRouter.of(context);
    // Add listener for route changes
    _router.routerDelegate.addListener(_onRouteChanged);

    // Handle initial route
    if (_isInitialRoute) {
      _isInitialRoute = false;
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _scrollToActiveItem();
      });
    }
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _router.routerDelegate.removeListener(_onRouteChanged);
    super.dispose();
  }

  void _onRouteChanged() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _scrollToActiveItem();
    });
  }

  void _scrollToActiveItem() {
    if (!mounted) return;

    final activeIndex = sideBarItems.indexWhere(_isActiveRoute);
    if (activeIndex != -1 && _scrollController.hasClients) {
      // Calculate the position to scroll to
      final itemPosition = activeIndex * 75.0; // 75 is the itemExtent
      final viewportHeight = _scrollController.position.viewportDimension;

      // If the item is below the viewport, scroll to it
      if (itemPosition > _scrollController.offset + viewportHeight - 75) {
        _scrollController.animateTo(
          itemPosition - viewportHeight + 75,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      }
      // If the item is above the viewport, scroll to it
      else if (itemPosition < _scrollController.offset) {
        _scrollController.animateTo(
          itemPosition,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final textTheme = Theme.of(context).textTheme;
    final isExtended = MediaQuery.of(context).size.width > kSideBarMinWidth;

    final purchaseOrderEnabled =
        ref.watch(isFeatureEnabledProvider(AuthScope.purchaseOrder));
    final salesOrderEnabled =
        ref.watch(isFeatureEnabledProvider(AuthScope.salesOrder));
    final globalOrderEnabled =
        ref.watch(isFeatureEnabledProvider(AuthScope.globalOrder));
    final payoutEnabled = ref.watch(isFeatureEnabledProvider(AuthScope.payout));
    final deliveryEnabled =
        ref.watch(isFeatureEnabledProvider(AuthScope.delivery));

    sideBarItems = [
      SideBarItem('Home', 'Console overview', kHomeRoute, kBuildingStoreSvg),
      if (purchaseOrderEnabled || globalOrderEnabled)
        SideBarItem('Purchase Orders', 'Create and track purchase orders',
            kOrdersRoute, kBoxSvg),
      if (purchaseOrderEnabled || globalOrderEnabled)
        SideBarItem('Purchase Invoices', 'Review and pay invoices',
            kInvoicesRoute, kChecklistSvg),
      if (salesOrderEnabled)
        SideBarItem('Sales Orders', 'Create and track sales orders',
            kSalesOrdersRoute, kSalesOrderSvg),
      if (salesOrderEnabled)
        SideBarItem('Sales Invoices', 'Review and pay sales invoices',
            kSalesInvoicesRoute, kBagSvg),
      if (globalOrderEnabled)
        SideBarItem(
            'Shipments', 'Track your shipments', kShipmentsRoute, kShipmentSvg),
      if (deliveryEnabled)
        SideBarItem('Deliveries', 'Track delivery trips', kDeliveriesRoute,
            kDeliveriesSvg),
      if (payoutEnabled)
        SideBarItem(
            'Payouts', 'See your payout history', kPayoutRoute, kPayOutSvg),
      SideBarItem('Catalog', 'Browse available products', kCatalogRoute,
          kShoppingCartSvg),
      SideBarItem('Account Statement', 'Track account activities',
          kAccountRoute, kReceiptSvg)
    ];

    return ConstrainedBox(
      constraints: BoxConstraints.loose(
        Size(isExtended ? 300 : 120, double.maxFinite),
      ),
      child: DecoratedBox(
        decoration: BoxDecoration(
          color: Palette.kFCFCFC,
        ),
        child: Padding(
          padding:
              const EdgeInsets.only(left: 40, right: 30, top: 20, bottom: 30),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.only(left: 8),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    CircleAvatar(
                      radius: 16,
                      backgroundColor: Palette.kEFEFEF,
                      child: SvgPicture.asset(
                        kBuildingsSvg,
                        colorFilter:
                            ColorFilter.mode(Palette.k6B797C, BlendMode.srcIn),
                      ),
                    ),
                    if (isExtended) ...[
                      const Gap(7),
                      Text(
                        ref
                                .read(userControllerProvider)
                                ?.currentRetailOutlet
                                ?.outletBusinessName ??
                            'Anon',
                        style: textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.w500,
                        ),
                      )
                    ]
                  ],
                ),
              ),
              const Gap(50),
              Expanded(
                child: ListView(
                  key: _listKey,
                  controller: _scrollController,
                  physics: const ClampingScrollPhysics(),
                  itemExtent: 75,
                  children: sideBarItems
                      .map(
                        (item) => SideBarTile(
                          key: ValueKey(item.route),
                          item: item,
                          isActive: _isActiveRoute(item),
                        ),
                      )
                      .toList(),
                ),
              ),
              const Gap(30),
              const LogOutTile(),
            ],
          ),
        ),
      ),
    );
  }
}

class SideBarItem {
  final String title;
  final String subTitle;
  final String icon;
  final String route;

  SideBarItem(this.title, this.subTitle, this.route, this.icon);
}

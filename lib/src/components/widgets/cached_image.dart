import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:td_commons_flutter/constants/app_values.dart';
import 'package:td_procurement/core/DI/di_providers.dart';
import 'package:td_procurement/src/services/image_cache_service.dart';
import 'package:td_procurement/src/utils/helpers/methods.dart';

enum ImageSize { small, large }

/// A robust image loading widget that handles CORS issues and provides efficient caching
class CachedImage extends ConsumerStatefulWidget {
  final String? variantId;
  final ImageSize size;
  final BoxFit? fit;
  final double? width;
  final double? height;
  final Widget? placeholder;
  final Widget? errorWidget;

  const CachedImage(
    this.variantId,
    this.size, {
    super.key,
    this.fit,
    this.width,
    this.height,
    this.placeholder,
    this.errorWidget,
  });

  @override
  ConsumerState<CachedImage> createState() => _CachedImageState();
}

class _CachedImageState extends ConsumerState<CachedImage> {
  String? _successfulUrl;
  bool _isLoading = true;
  bool _hasError = false;

  @override
  void initState() {
    super.initState();
    _loadImage();
  }

  @override
  void didUpdateWidget(CachedImage oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.variantId != widget.variantId ||
        oldWidget.size != widget.size) {
      _loadImage();
    }
  }

  String _getImageUrl(WidgetRef ref, [bool useProdUrl = false]) {
    final config = ref.read(appConfigProvider);
    switch (widget.size) {
      case ImageSize.small:
        return getSmallImageURL(widget.variantId, config.env, useProdUrl);
      case ImageSize.large:
        return getLargeImageURL(widget.variantId, config.env, useProdUrl);
    }
  }

  Future<void> _loadImage() async {
    if (widget.variantId == null || widget.variantId!.isEmpty) {
      setState(() {
        _isLoading = false;
        _hasError = true;
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _hasError = false;
      _successfulUrl = null;
    });

    // Get candidate URLs in order of preference
    final candidateUrls = [
      _getImageUrl(ref, true), // Production URL first
      _getImageUrl(ref, false), // Development URL as fallback
    ].where((url) => url.isNotEmpty).toList();

    // Use the image cache service to find a working URL
    final imageService = ImageCacheService();
    final workingUrl = await imageService.getWorkingImageUrl(
      variantId: widget.variantId!,
      candidateUrls: candidateUrls,
    );

    setState(() {
      _isLoading = false;
      if (workingUrl != null) {
        _successfulUrl = workingUrl;
        _hasError = false;
      } else {
        _hasError = true;
      }
    });
  }

  Widget _buildPlaceholder() {
    return widget.placeholder ??
        Image.asset(
          PLACEHOLDER_URL,
          package: PACKAGE_NAME,
          width: widget.width,
          height: widget.height,
          fit: widget.fit ?? BoxFit.cover,
        );
  }

  Widget _buildErrorWidget() {
    return widget.errorWidget ?? _buildPlaceholder();
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return _buildPlaceholder();
    }

    if (_hasError || _successfulUrl == null) {
      return _buildErrorWidget();
    }

    // Use Image.network with the successful URL
    // This approach avoids CORS issues by letting Flutter handle the image loading
    return Image.network(
      _successfulUrl!,
      width: widget.width,
      height: widget.height,
      fit: widget.fit ?? BoxFit.cover,
      loadingBuilder: (context, child, loadingProgress) {
        if (loadingProgress == null) return child;
        return _buildPlaceholder();
      },
      errorBuilder: (context, error, stackTrace) => _buildErrorWidget(),
    );
  }
}

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:td_commons_flutter/models/order_preview_detail.dart';
import 'package:td_commons_flutter/models/retail_branch.dart';
import 'package:td_commons_flutter/models/retailer.dart';
import 'package:td_procurement/app/auth/domain/logic/controllers/user_controller.dart';
import 'package:td_procurement/src/res/values/constants/constants.dart';

final branchesProvider = Provider.autoDispose<List<RetailBranch>>((ref) {
  final outlet = ref.read(userControllerProvider)?.currentRetailOutlet;
  return outlet != null
      ? [
          RetailBranch(
            id: outlet.id ?? '',
            outletBusinessName: outlet.outletBusinessName ?? '',
            streetName: outlet.formattedAddress ?? outlet.streetName ?? '',
            contactPhone: outlet.contactPhone ?? '',
          ),
          ...?outlet.branches,
        ]
      : [];
});

final hexCodeProvider = Provider.autoDispose<String?>((ref) {
  final outlet = ref.read(userControllerProvider)?.currentRetailOutlet;
  return outlet?.coordinates?.plusCode6Hex;
});

final countryCodeProvider = Provider.autoDispose<String?>((ref) {
  final outlet = ref.read(userControllerProvider)?.currentRetailOutlet;
  return outlet?.country;
});

final outletIdProvider = Provider.autoDispose<String?>((ref) {
  final outlet = ref.read(userControllerProvider)?.currentRetailOutlet;
  return outlet?.id;
});

final currencyCodeProvider = Provider.autoDispose<String>((ref) {
  final countryCode = ref.read(countryCodeProvider);
  return countryCode == null
      ? kDefaultGBCurrency
      : countryToCurrency[countryCode] ?? kDefaultGBCurrency;
});

final countryTypeProvider = Provider.autoDispose<CountryType>((ref) {
  // final outlet = ref.read(userControllerProvider)?.currentRetailOutlet;
  // return outlet?.salesChannel?.toLowerCase() == 'export'
  //     ? CountryType.export
  //     : CountryType.nonExport;

  return ref.read(isFeatureEnabledProvider(AuthScope.globalOrder))
      ? CountryType.export
      : CountryType.nonExport;
});

enum CountryType {
  export,
  nonExport;

  /// Provides pattern-matching like behavior for [CountryType].
  ///
  /// Example:
  /// ```dart
  /// final countryType = ref.read(countryTypeProvider);
  /// countryType.when(
  ///   export: () => print('Export logic'),
  ///   nonExport: () => print('Non-export logic'),
  /// );
  /// ```
  ///
  /// - [export] is called when the value is [CountryType.export].
  /// - [nonExport] is called when the value is [CountryType.nonExport].
  T when<T>({
    required T Function() export,
    required T Function() nonExport,
  }) {
    switch (this) {
      case CountryType.export:
        return export();
      case CountryType.nonExport:
        return nonExport();
    }
  }
}

final isSalesOrderCustomerProvider = Provider.autoDispose<bool>((ref) {
  final outlet = ref.read(userControllerProvider)?.currentRetailOutlet;
  return outlet?.isSalesOrderCustomer == true;
});

final featuresProvider = Provider.autoDispose<List<AuthScope>>((ref) {
  final outlet = ref.watch(userControllerProvider)?.currentRetailOutlet;
  return outlet?.authScopes ?? [];
});

final isFeatureEnabledProvider =
    Provider.autoDispose.family<bool, AuthScope>((ref, scope) {
  final enabledFeatures = ref.watch(featuresProvider);
  return enabledFeatures.contains(scope);
});

final isMasterDistributorProvider = Provider.autoDispose<bool>((ref) {
  return ref
          .watch(userControllerProvider)
          ?.currentRetailOutlet
          ?.outletType
          ?.toLowerCase() ==
      'master distributor';
});

final orderPreviewDetailProvider =
    StateProvider<OrderPreviewDetail?>((_) => null);
import 'package:dlibphonenumber/phone_number_util.dart';
import 'package:flutter/services.dart';

class Validators {
  static bool validateEmail(String value) {
    return RegExp(
            r"^[a-zA-Z0-9.a-zA-Z0-9.!#$%&'*+-/=?^_`{|}~]+@[a-zA-Z0-9]+\.[a-zA-Z]+")
        .hasMatch(value);
  }

  static Future<String?> validatePhoneNumber(
      String number, String countryCode) async {
    try {
      final PhoneNumberUtil phoneUtil = PhoneNumberUtil.instance;
      final num = phoneUtil.parse(number, countryCode);
      if(phoneUtil.isValidNumber(num)){
        return "+${num.countryCode}${num.nationalNumber}";
      }
      return null;
    } catch (err) {
      return null;
    }
  }

  static String? validatePassword(String? password) {
    RegExp numeric = RegExp(r'^-?\d+$');
    if (password?.isEmpty ?? true) {
      return 'Please provide your password';
    }
    if (password!.length < 8) {
      return 'Password must be at least 8 characters';
    }
    if (numeric.hasMatch(password)) {
      return 'Password must contain alphabets';
    }
    return null;
  }

  static String? Function(String?) validateDiffChange(
    String field, [
    String? error,
  ]) {
    return (String? value) {
      if (field != value) {
        return error ?? 'Values don\'t match';
      }
      return null;
    };
  }

  static String? confirmPassword(String? value, String passwordField) {
    if (value!.isEmpty) {
      return 'Please enter a password.';
    }
    return validateDiffChange(
      passwordField,
      'The passwords don\'t match',
    )(value);
  }

  static String? verifyPassword(String? value) {
    if (value!.isEmpty) {
      return 'Please enter a password';
    }
    return null;
  }

  static String? verifyEmail(String? value) {
    const errMsg = 'Please enter a valid email address';
    value = value?.trim();
    if (value?.isEmpty ?? true) {
      return errMsg;
    }
    return validateEmail(value!) ? null : errMsg;
  }

  static String? verifyInput(String? value,
      {String field = 'Field', int length = 1}) {
      value = value?.trim();
    if (value?.isEmpty ?? true) {
      return '$field is required';
    } else if (value!.length < length) {
      return '$length characters ${length > 1 ? 'are':'is'} required';
    }
    return null;
  }

  static TextInputFormatter validInput() {
    return FilteringTextInputFormatter.allow(
      RegExp("[a-zA-Z0-9 ]"),
    );
  }

  static TextInputFormatter validNumberInput() {
    return FilteringTextInputFormatter.allow(
      RegExp("[0-9+]"),
    );
  }

}

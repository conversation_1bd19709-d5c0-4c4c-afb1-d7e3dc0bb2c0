import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:sentry_flutter/sentry_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:td_procurement/core/DI/di_providers.dart';
import 'package:td_procurement/core/services/shared_prefs/shared_prefs.dart';
import 'package:td_procurement/procurement_app.dart';

import 'configure_non_web.dart'
    if (dart.library.js_interop) 'configure_web.dart';
import 'core/env/.env.dart';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await SentryFlutter.init(
    (options) {
      options.dsn = appConfig['SENTRY_DSN'];
      options.environment = appConfig['FLUTTER_APP_FLAVOR'];
    },
    appRunner: () async {
      await ScreenUtil.ensureScreenSize();

      final prefs = await SharedPreferences.getInstance();
      final sharedPref = SharedPrefs()..initPreferences = prefs;

      configureApp();
      SystemChrome.setSystemUIOverlayStyle(
        SystemUiOverlayStyle.dark.copyWith(
          statusBarColor: Colors.black,
          systemNavigationBarColor: Colors.black,
          statusBarBrightness: Brightness.dark,
        ),
      );
      runApp(
        ProviderScope(
          overrides: [storageProvider.overrideWithValue(sharedPref)],
          child: const ProcurementApp(),
        ),
      );
    },
  );
}

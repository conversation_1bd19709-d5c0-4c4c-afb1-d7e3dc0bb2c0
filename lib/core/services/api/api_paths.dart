const kLoginApiPath = '/shop/v4/loginWithToken';
const kCheckPhoneApiPath = '/shop/v4/getPhoneStatus';
const kSendOtpApiPath = '/phoneauth/send-totp';
const kVerifyOtpApiPath = '/phoneauth/verify-totp';
const kTransactionApiPath = '/api/v3/procurement/transactions?:queryString';
const kOrderDetailsApiPath = '/api/v3/procurement/transactions/:orderId';
const kCreateApiPath = '/api/v3/procurement/key_account/create';
const kRegisterPartnerApiPath = '/hubspot-proxy';
const kShipmentApiPath = '/api/v3/order-shipment/tracking/:shipmentId';
const kFetchOutletApiPath =
    '/api/v4/procurement/retail/get-outlet-types?country=NG';
const kProxyApiPath = '/shop/v3/proxy';
const kDeleteAccountApiPath = '/api/v3/retail/delete/account';
const kAccountStatementApiPath = '/account-statement';
const kInvoicesApiPath = '/invoices';
const kSalesInvoicesApiPath = '/api/v3/sales/invoices';
const kGetInvoiceApiPath = '/api/v3/get-invoice';
// const kGetInvoiceDetailApiPath =
//     '/api/v3/manager-invoice/:retailOutletId/:orderId';
const kSalesInvoiceDetailsPath = '/api/v3/sales-invoice/:orderId';
const kPurchaseInvoiceDetailsPath = '/api/v3/get-invoice/:orderId';
const kGetPayoutDetailsApiPath =
    '/api/v3/partners/collection?reference=:reference';
const kGetSalesInvoiceApiPath = '/api/v3/sales-invoice';
const kCollectionApiPath =
    '/shop/v5/procurement/getPageCollection?page=browse&plusCode6Hex=:hexCode';
const kVariantSearchApiPath =
    '/collections-search?plusCode=:hexCode&term=:searchTerm&page=1&perPage=50';
const kPrepareNonExportOrderApiPath = '/api/v2/retail/orders/prepare';
const kPrepareExportOrderApiPath = '/api/v2/global/orders/prepare';
const kShippingRateApiPath = '/api/v3/shipping/rate';
const kDraftOrderApiPath = '/draft-order';
const kCreateCartApiPath = '/create-cart';
const kCreateOrderApiPath = '/create-order';
const kOutletApiPath =
    '/api/v4/procurement/get-retail-outlet?outletId=:outletId';
const kChargeOrderApiPath = '/charge-order';
const kCreateExportOrderApiPath = '/api/v2/global/ext/orders';
const kCreateSalesOrderApiPath = '/api/v2/retail/ext/orders';
const kOutletVariantsApiPath = '/outlet-variants';
const kExportVariantsApiPath =
    '/export-variants?batch=1&limit=1000&sortBy=dateAdded';
const kShipmentsApiPath = '/api/v3/procurement/get_shipments?:queryString';
const kShipmentPath = '/api/v3/ordershipments/:id';
const kDocumentsApiPath = '/api/v3/ordershipments/:id/documents';
const kInvoiceApiPath = '/api/v3/get-invoice/:orderId';
const kSalesOrdersApiPath = '/api/v3/sales/orders?:queryString';
const kSalesOrderApiPath = '/api/v3/sales/orders/:id';
const kCustomerApiPath = '/api/v3/procurement/get_customer';
const supplierInventoryApiPath = '/api/v3/inventory/supplier/:outletId';
const orderDeliveryApiPath = '/api/v3/orders/ship';
const kSupplierInventoryApiPath = '/api/v3/inventory/supplier/:outletId';
const kOrderAcceptanceApiPath = '/api/v3/global/order/accept/:reference';
const kFetchDeliveriesApiPath =
    '/api/v3/deliveries/locations/:locationId?:queryString';
const kFetchDeliveryOrdersApiPath = '/api/v3/deliveries/:deliveryId/orders';
const kDeliveryIDApiPath = '/api/v3/deliveries/:deliveryId';
const kSalesLocationDriversApiPath = '/api/v3/drivers/:locationId';
const kAssignDriverToOrdersApiPath = '/api/v3/drivers/:locationId/assignment';
